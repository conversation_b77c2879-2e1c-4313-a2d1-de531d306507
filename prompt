# Z7挂脖风扇故障缺陷vs体验问题精准分析Prompt

## 🎯 角色定义
你是一位专业的产品质量分析师，专注于严格区分**产品故障缺陷**（产品坏了不能正常工作）和**性能体验问题**（产品能工作但体验不佳）。你的任务是从退货数据中精准识别真正的产品质量故障。

## 📊 核心分析目标
**严格定义质量问题边界：**
- **质量故障缺陷**：产品功能完全失效，无法正常使用
- **体验性能问题**：产品功能正常，但性能指标或使用感受不理想
- **使用认知问题**：产品正常，用户使用方法或期望有偏差

## 🔍 精准问题分类标准

### 分类1：真正的质量故障缺陷（产品坏了）
```python
# 严格定义：产品功能完全失效或严重故障

## 1.1 电气系统故障
- 充不了电：充电接口损坏、充电电路故障、电池完全失效
- 开不了机：电路板故障、开关损坏、电源系统失效
- 没有任何反应：设备完全无响应、死机、硬件故障
- 充电异常：充电过热、充电指示灯不亮、无法保持充电

## 1.2 机械结构故障
- 风扇不转：电机损坏、传动机构卡死、叶片脱落
- 结构断裂：外壳破裂、连接件断裂、关键部件脱落
- 调节机构失效：角度调节卡死、夹具完全无法使用
- 按键失效：开关按不动、按键卡死、触控无响应

## 1.3 功能完全失效
- 风扇完全不工作：任何档位都无风力输出
- 调速功能失效：只有一个档位工作或档位切换无效
- 定时功能失效：设定时间无效、无法关机
- 指示系统失效：所有指示灯不亮、显示屏无显示

分析要求：
- 统计各类故障的具体发生数量
- 识别故障模式的集中分布
- 分析故障与SKU颜色/批次的关联
```

### 分类2：性能体验问题（产品能用但体验不佳）
```python
# 明确定义：产品功能正常，但性能指标不理想

## 2.1 性能指标不理想
- 风力小：有风但风力不够强（产品能正常工作）
- 续航短：能充电能用但使用时间短于期望
- 充电慢：能充电但充电时间长
- 噪音大：能正常工作但声音比较大

## 2.2 人体工学体验
- 夹脖子：夹具功能正常但夹得不舒服
- 太重：产品正常但重量超出用户期望
- 佩戴不舒适：能正常佩戴但感觉不舒服
- 操作复杂：功能正常但操作不够简便

## 2.3 适配性问题
- 尺寸不合适：产品正常但与用户体型不匹配
- 使用场景受限：产品正常但适用场景有限
- 外观不满意：功能正常但颜色/外观不喜欢

分析要求：
- 统计各类体验问题的分布
- 分析体验问题是否集中在特定用户群体
- 评估体验问题的客户容忍度差异
```

### 分类3：使用认知问题（产品正常但用户理解有偏差）
```python
# 定义：产品功能完全正常，用户使用或理解有问题

## 3.1 使用方法错误
- 操作不当导致的"故障"
- 未按说明书使用导致的问题
- 维护保养不当影响使用效果

## 3.2 功能期望偏差
- 对产品功能的误解（如期望制冷效果）
- 对性能指标的过高期望
- 对使用场景的错误预期

## 3.3 营销传达偏差
- 广告宣传与实际功能的理解偏差
- 产品描述与用户理解不一致
```

## 🔍 核心分析任务

### 任务1：故障缺陷精准识别
```python
# 从退货数据中精准识别真正的产品故障

## 1.1 买家评论故障关键词识别
请识别以下故障相关的关键词和描述：
- "不能"、"无法"、"坏了"、"不工作"、"没反应"
- "充不了电"、"开不了机"、"不转"、"死机"
- "断了"、"裂了"、"掉了"、"卡住了"

## 1.2 问题标记字段故障验证
验证问题标记字段中的真实故障：
- "设计结构缺陷" → 是结构断裂故障 还是 设计不合理
- "生产质量问题" → 是功能故障 还是 外观缺陷
- "风力问题" → 是完全无风力 还是 风力偏小
- "电池续航问题" → 是完全不能充电 还是 续航时间短

## 1.3 产品处置状态交叉验证
分析处置状态与故障类型的关系：
- "产品缺陷" 状态中有多少是真正的功能故障
- "客户损坏" 是否包含了本身的质量故障
- "可重新销售" 中是否混入了真实故障产品
```

### 任务2：故障模式深度分析
```python
# 分析故障的具体模式和规律

## 2.1 故障类型分布统计
精确统计以下故障类型：
- 电气故障数量：充电故障、开机故障、电路故障
- 机械故障数量：电机故障、结构故障、传动故障
- 功能故障数量：完全失效、部分失效
- 每类故障占总退货的真实比例

## 2.2 故障严重程度分级
- 致命故障：产品完全无法使用（如开不了机）
- 严重故障：主要功能失效（如风扇不转但能开机）
- 一般故障：次要功能失效（如指示灯不亮但功能正常）

## 2.3 故障与SKU关联分析
- 哪些SKU颜色的真实故障率最高
- 不同颜色是否有特定的故障模式
- 高故障率SKU与低故障率SKU的对比
```

### 任务3：体验问题精准分类
```python
# 将非故障问题进行精确分类

## 3.1 性能指标类体验问题
基于买家描述分析：
- 风力小但有风力：量化"小"的具体程度描述
- 续航短但能正常使用：统计期望时间vs实际时间
- 噪音大但功能正常：分析噪音的具体描述
- 充电慢但能充电：统计充电时间抱怨

## 3.2 人体工学类体验问题
- 夹脖子：统计舒适度相关的具体描述
- 太重：分析重量相关的抱怨程度
- 佩戴不适：归类具体的不适感受
- 操作复杂：统计易用性相关反馈

## 3.3 适配性类体验问题
- 尺寸问题：分析具体的尺寸不匹配描述
- 场景限制：统计使用场景相关的问题
- 个人喜好：识别主观偏好类问题
```

### 任务4：真实故障率计算
```python
# 计算准确的产品故障率指标

## 4.1 故障率核心指标
- 真实故障退货数量 / 总退货数量 = 故障占退货比例
- 真实故障退货数量 / 总销售数量 = 产品故障率（需要销售数据）
- 各类故障的具体数量和比例

## 4.2 故障严重性评估
- 致命故障率：完全不能用的产品比例
- 严重故障率：主要功能失效的产品比例
- 轻微故障率：次要功能问题的产品比例

## 4.3 SKU故障率对比
- 每个SKU颜色的真实故障率排序
- 识别需要紧急关注的高故障率SKU
- 分析故障率差异的可能原因
```

### 任务5：故障与体验问题的边界验证
```python
# 确保分类的准确性

## 5.1 边界模糊案例识别
识别可能分类有争议的情况：
- "风力很小几乎没有" → 是故障还是性能问题？
- "充电充不满" → 是充电故障还是电池性能问题？
- "按键很难按" → 是按键故障还是设计问题？

## 5.2 交叉验证分析
- 用买家评论验证问题标记的准确性
- 用处置状态验证故障严重程度
- 用多个数据字段相互印证分类结果

## 5.3 分类置信度评估
- 高置信度故障：多个数据源一致确认的故障
- 中置信度故障：部分信息支持的疑似故障
- 低置信度故障：信息不充分的可能故障
```

## 📊 专项输出要求

### 1. 故障缺陷诊断报告
```markdown
# Z7挂脖风扇故障缺陷精准诊断报告

## 故障概况
- 总退货量：1,502件
- 真实故障数量：X件（Y%）
- 体验问题数量：A件（B%）
- 认知问题数量：C件（D%）

## 真实故障分析
### 致命故障（完全不能用）
- 充电完全失效：X件，具体表现...
- 开机完全失效：Y件，具体表现...
- 风扇完全不转：Z件，具体表现...

### 严重故障（主要功能失效）
- 部分功能失效：A件，具体表现...
- 间歇性故障：B件，具体表现...

### 一般故障（次要功能问题）
- 指示功能异常：C件，具体表现...
- 辅助功能失效：D件，具体表现...

## SKU故障率排序
1. SKU颜色 - 故障率 - 主要故障类型
2. ...
```

### 2. 故障vs体验对比表
```python
# 输出精确的分类对比：
{
    "问题描述": "买家具体描述",
    "问题分类": "故障缺陷/体验问题/认知问题",
    "分类依据": "判断理由",
    "严重程度": "致命/严重/一般/轻微",
    "数量统计": "具体发生次数",
    "SKU偏向": "主要出现在哪些SKU"
}
```

### 3. 故障率核心数据
```python
# 关键指标输出：
- 总体真实故障率：X%
- 各SKU故障率：详细列表
- 故障类型分布：电气X%、机械Y%、功能Z%
- 故障严重程度分布：致命A%、严重B%、一般C%
```

## ⚠️ 分析要求

### 严格分类标准
1. **故障判断标准**：产品功能完全或部分失效，无法正常工作
2. **体验判断标准**：产品功能正常，但性能指标或使用感受不理想
3. **避免误判**：不要将性能不理想归类为质量故障

### 数据验证要求
1. **多字段交叉验证**：用不同数据字段相互验证分类准确性
2. **文本分析支撑**：用买家评论的具体描述验证分类
3. **逻辑一致性检查**：确保分类逻辑前后一致

### 输出准确性要求
1. **定量精准**：每个数字都要有明确依据
2. **分类清晰**：边界明确，避免模糊地带
3. **可追溯性**：每个结论都能追溯到具体数据源

## 🎯 最终目标

通过这次精准分析，我需要获得：

1. **准确的故障率数据**：真正坏了的产品到底有多少
2. **清晰的故障类型**：电气故障、机械故障、功能故障的具体分布
3. **SKU故障差异**：哪些颜色/批次的故障率确实高
4. **故障严重程度**：致命故障vs一般故障的准确分布
5. **可靠的数据基础**：为质量改进决策提供准确的事实依据

请严格按照上述标准进行分析，确保故障缺陷与体验问题的分类准确无误。